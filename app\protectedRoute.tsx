'use client'

import { useEffect, useState } from 'react'
import { getWorkCodeFromUrl } from '@/service/fetch'
import { useRouter, usePathname } from 'next/navigation'
import Toast from '@/app/components/base/toast'

// 立即执行的认证初始化函数
const initAuth = () => {
  try {
    // 检查URL中是否有workcode参数
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const workcode = urlParams.get('workcode') || localStorage.getItem('workcode')
      
      // 如果URL中有workcode参数，立即设置临时token
      if (workcode) {
        // console.log('立即设置token - workcode:', workcode)
        const tempToken = 'auto_login_' + Math.random().toString(36).substring(2)
        localStorage.setItem('console_token', tempToken)
        localStorage.setItem('refresh_token', tempToken)
        return true
      }
    }
  } catch (error) {
    console.error('认证初始化错误:', error)
  }
  return false
}

// 在模块加载时立即执行，确保在任何API请求前完成认证
initAuth()

// 保护路由组件，用于检查登录状态并自动登录
export default function ProtectedRoute({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  const router = useRouter()
  const pathname = usePathname()
  const [isAuthenticated, setIsAuthenticated] = useState(!!initAuth())
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 检查是否有workcode参数
        const workcode = getWorkCodeFromUrl()
        if (workcode) {
          console.log('检测到workcode参数，跳过认证:', workcode)
          setIsAuthenticated(true)
          setIsChecking(false)
          return
        }

        // 检查localStorage中的token
        const hasToken = localStorage.getItem('console_token')
        if (hasToken) {
          setIsAuthenticated(true)
          setIsChecking(false)
          return
        }

        // 如果在登录页或注册页则跳过检查
        if (pathname === '/signin' || pathname === '/signup') {
          setIsAuthenticated(true)
          setIsChecking(false)
          return
        }

        // 没有认证，重定向到登录页
        console.log('未认证，重定向到登录页')
        router.replace('/signin')
      } catch (error) {
        console.error('认证检查出错:', error)
        Toast.notify({
          type: 'error',
          message: '认证检查失败',
        })
        setIsAuthenticated(false)
      } finally {
        setIsChecking(false)
      }
    }

    // 如果在初始化时没有设置认证，则运行完整的认证检查
    if (!isAuthenticated) {
      checkAuth()
    } else {
      setIsChecking(false) // 已认证，无需等待
    }
  }, [pathname, router, isAuthenticated])

  // 加载中显示空内容
  if (isChecking) {
    return null
  }

  // 已认证显示子组件
  if (isAuthenticated) {
    return <>{children}</>
  }

  // 未认证时显示空内容（已经在checkAuth中处理重定向）
  return null
} 