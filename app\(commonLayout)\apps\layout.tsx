import { redirect } from 'next/navigation'

export default function AppsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // 获取查询参数
  const searchParams = new URL(process.env.NEXT_PUBLIC_API_PREFIX || 'http://localhost:3000').searchParams
  const hasWorkcode = searchParams.has('workcode')
  
  // 如果有workcode参数，则跳过认证检查
  if (hasWorkcode) {
    return <>{children}</>
  }
  
  // 如果没有workcode，正常显示内容，登录检查由客户端处理
  return <>{children}</>
} 