'use client'

import type { FC, PropsWithChildren } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
// 仅在开发环境中导入
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

const STALE_TIME = 1000 * 60 * 30 // 30 minutes

const client = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: STALE_TIME,
    },
  },
})

export const TanstackQueryIniter: FC<PropsWithChildren> = (props) => {
  const { children } = props
  return <QueryClientProvider client={client}>
    {children}
    {/* 完全移除 Devtools
    <ReactQueryDevtools initialIsOpen={false} /> 
    
    或者在非生产环境下才显示:
    {process.env.NODE_ENV !== 'production' && <ReactQueryDevtools initialIsOpen={false} />}
    
    或者修改位置和样式:
    <ReactQueryDevtools initialIsOpen={false} position="top-left" />
    */}
  </QueryClientProvider>
}
