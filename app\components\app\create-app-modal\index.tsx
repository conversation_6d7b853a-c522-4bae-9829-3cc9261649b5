'use client'

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from 'react'
import Radio from '@/app/components/base/radio'
import { useTranslation } from 'react-i18next'

import { useRouter, useSearchParams } from 'next/navigation'
import { useContext, useContextSelector } from 'use-context-selector'
import { RiArrowRightLine, RiCommandLine, RiCornerDownLeftLine, RiExchange2Fill } from '@remixicon/react'
import Link from 'next/link'
import { useDebounceFn, useKeyPress } from 'ahooks'
import Image from 'next/image'
import AppIconPicker from '../../base/app-icon-picker'
import type { AppIconSelection } from '../../base/app-icon-picker'
import Button from '@/app/components/base/button'
import Divider from '@/app/components/base/divider'
import cn from '@/utils/classnames'
import { WEB_PREFIX } from '@/config'
import AppsContext, { useAppContext } from '@/context/app-context'
import { useProviderContext } from '@/context/provider-context'
import { ToastContext } from '@/app/components/base/toast'
type AppMode = 'agent-chat' | 'workflow' // 简化类型定义，只保留需要的模式
// import { AppModes } from '@/types/app' // 不再需要原始模式定义
import { createApp } from '@/service/apps'
import Input from '@/app/components/base/input'
import Textarea from '@/app/components/base/textarea'
// import Select from '@/app/components/base/select'
import AppIcon from '@/app/components/base/app-icon'
import AppsFull from '@/app/components/billing/apps-full-in-dialog'
import { Logic } from '@/app/components/base/icons/src/vender/solid/communication' // 只保留agent和工作流相关图标
import { NEED_REFRESH_APP_LIST_KEY } from '@/config'
import { getRedirection } from '@/utils/app-redirection'
import FullScreenModal from '@/app/components/base/fullscreen-modal'
import useTheme from '@/hooks/use-theme'
import { apiPrefix } from '@/config'
import { get } from '@/service/base' // 添加导入get函数
import Select, { components } from 'react-select';
import makeAnimated from 'react-select/animated';
import { FixedSizeList as List } from 'react-window';

const animatedComponents = makeAnimated();

// 创建虚拟列表菜单组件
const MenuList = (props: any) => {
  // 安全检查，确保有子元素
  if (!props.children || !Array.isArray(props.children) || props.children.length === 0) {
    return <div>无数据</div>;
  }

  // 计算列表高度，但最大不超过 maxHeight
  const height = Math.min(35 * props.children.length, props.maxHeight);

  return (
    <List
      width="100%"
      height={height}
      itemCount={props.children.length}
      itemSize={35}
      initialScrollOffset={0}
    >
      {({ index, style }) => (
        <div style={style}>
          {props.children[index]}
        </div>
      )}
    </List>
  );
};

type CreateAppProps = {
  onSuccess: () => void
  onClose: () => void
  onCreateFromTemplate?: () => void
}

function CreateApp({ onClose, onSuccess, onCreateFromTemplate }: CreateAppProps) {
  const { t } = useTranslation()
  const { push } = useRouter()
  const { notify } = useContext(ToastContext)
  const mutateApps = useContextSelector(AppsContext, state => state.mutateApps)

  const [appMode, setAppMode] = useState<AppMode>('workflow')
  const [appIcon, setAppIcon] = useState<AppIconSelection>({ type: 'emoji', icon: '🤖', background: '#FFEAD5' })
  const [showAppIconPicker, setShowAppIconPicker] = useState(false)
  const [name, setName] = useState<String>('')
  const [description, setDescription] = useState<String>('')
  const [owner, setOwner] = useState<string[]>([]) // 选中的负责人，改为字符串数组
  const [ownerList, setOwnerList] = useState<Array<{ value: string; label: string }>>([]) // 负责人列表
  const [isLoadingOwners, setIsLoadingOwners] = useState<Boolean>(false) // 添加加载状态
  const [ownerSearchInput, setOwnerSearchInput] = useState<String>('') // 搜索关键词

  // 监听 owner 变化
  useEffect(() => {
    console.log('Owner 负责人更新:', owner)
  }, [owner])

  // 注释掉计费相关代码
  // const { plan, enableBilling } = useProviderContext()
  // const isAppsFull = (enableBilling && plan.usage.buildApps >= plan.total.buildApps)
  const { isCurrentWorkspaceEditor } = useAppContext()

  const isCreatingRef = useRef(false)

  const searchParams = useSearchParams()

  // 从外部API获取负责人列表，添加分页和搜索功能
  useEffect(() => {
    const fetchOwners = async () => {
      try {
        setIsLoadingOwners(true)
        // 获取负责人数据
        const data = await get<string[]>('/account/list')
        // 确保数据是数组且不为空
        if (data && Array.isArray(data)) {
          setOwnerList(data.map((item: string) => ({
            value: item,
            label: item
          })))
        } else {
          setOwnerList([])
          console.error('获取到的负责人数据格式不正确:', data)
        }
      } catch (error) {
        console.error('Failed to fetch owners:', error)
        setOwnerList([])
      } finally {
        setIsLoadingOwners(false)
      }
    }

    // 添加防抖延迟，避免频繁请求
    const timer = setTimeout(fetchOwners, 300)
    return () => clearTimeout(timer)
  }, [ownerSearchInput]) // 添加搜索关键词作为依赖项

  // 处理搜索输入变化
  const handleOwnerSearchInputChange = (inputValue: string) => {
    setOwnerSearchInput(inputValue)
  }

  useEffect(() => {
    const category = searchParams.get('category')
    if (category && (category === 'agent-chat' || category === 'workflow')) // 严格模式检查
      setAppMode(category)
  }, [searchParams])

  const onCreate = useCallback(async () => {
    if (!appMode) {
      notify({ type: 'error', message: t('app.newApp.appTypeRequired') })
      return
    }
    if (!name.trim()) {
      notify({ type: 'error', message: t('app.newApp.nameNotEmpty') })
      return
    }
    if (isCreatingRef.current)
      return
    isCreatingRef.current = true
    try {
      console.log('Creating app with owner:', owner) // 添加日志
      const app = await createApp({
        name,
        description,
        icon_type: appIcon.type,
        icon: appIcon.type === 'emoji' ? appIcon.icon : appIcon.fileId,
        icon_background: appIcon.type === 'emoji' ? appIcon.background : undefined,
        mode: appMode,
        manager_list: owner && owner.length > 0 ? owner : [], // 确保传递正确的数组格式
      } as any)
      console.log('App created successfully:', app) // 添加日志
      notify({ type: 'success', message: t('app.newApp.appCreated') })
      onSuccess()
      onClose()
      mutateApps()
      localStorage.setItem(NEED_REFRESH_APP_LIST_KEY, '1')
      getRedirection(isCurrentWorkspaceEditor, app, push)
    }
    catch {
      notify({ type: 'error', message: t('app.newApp.appCreateFailed') })
    }
    isCreatingRef.current = false
  }, [name, notify, t, appMode, appIcon, description, onSuccess, onClose, mutateApps, push, isCurrentWorkspaceEditor, owner])

  const { run: handleCreateApp } = useDebounceFn(onCreate, { wait: 300 })
  useKeyPress(['meta.enter', 'ctrl.enter'], () => {
    // if (isAppsFull)
    //   return
    handleCreateApp()
  })
  return <>
    <div className='flex h-full justify-center overflow-y-auto overflow-x-hidden'>
      <div className='flex flex-1 shrink-0 justify-end'>
        <div className='px-10'>
          <div className='h-6 w-full 2xl:h-[139px]' />
          <div className='pb-6 pt-1'>
            <span className='title-2xl-semi-bold text-text-primary'>{t('app.newApp.startFromBlank')}</span>
          </div>
          <div className='mb-2 leading-6'>
            <span className='system-sm-semibold text-text-secondary'>{t('app.newApp.chooseAppType')}</span>
          </div>
          <div className='flex w-[660px] flex-col gap-4'>
            <div>
              <div className='mb-2'>
                <span className='system-2xs-medium-uppercase text-text-tertiary'>{t('app.newApp.forAdvanced')}</span>
              </div>
              <div className='flex flex-row gap-2'>
                <AppTypeCard
                  active={appMode === 'agent-chat'}
                  title={t('app.types.agent')}
                  description={t('app.newApp.agentShortDescription')}
                  icon={<div className='flex h-6 w-6 items-center justify-center rounded-md bg-components-icon-bg-violet-solid'>
                    <Logic className='h-4 w-4 text-components-avatar-shape-fill-stop-100' />
                  </div>}
                  onClick={() => {
                    setAppMode('agent-chat')
                  }} />
                <AppTypeCard
                  active={appMode === 'workflow'}
                  title={t('app.types.workflow')}
                  description={t('app.newApp.workflowShortDescription')}
                  icon={<div className='flex h-6 w-6 items-center justify-center rounded-md bg-components-icon-bg-indigo-solid'>
                    <RiExchange2Fill className='h-4 w-4 text-components-avatar-shape-fill-stop-100' />
                  </div>}
                  onClick={() => {
                    setAppMode('workflow')
                  }} />
              </div>
            </div>
            <Divider style={{ margin: 0 }} />
            <div className='flex items-center space-x-3'>
              <div className='flex-1'>
                <div className='mb-1 flex h-6 items-center'>
                  <label className='system-sm-semibold text-text-secondary'>{t('app.newApp.captionName')}</label>
                </div>
                <Input
                  value={name}
                  onChange={e => setName(e.target.value)}
                  placeholder={t('app.newApp.appNamePlaceholder') || ''}
                />
              </div>
              <AppIcon
                iconType={appIcon.type}
                icon={appIcon.type === 'emoji' ? appIcon.icon : appIcon.fileId}
                background={appIcon.type === 'emoji' ? appIcon.background : undefined}
                imageUrl={appIcon.type === 'image' ? appIcon.url : undefined}
                size='xxl' className='cursor-pointer rounded-2xl'
                onClick={() => { setShowAppIconPicker(true) }}
              />
              {showAppIconPicker && <AppIconPicker
                onSelect={(payload) => {
                  setAppIcon(payload)
                  setShowAppIconPicker(false)
                }}
                onClose={() => {
                  setShowAppIconPicker(false)
                }}
              />}
            </div>
            <div>
              <div className='mb-1 flex h-6 items-center'>
                <label className='system-sm-semibold text-text-secondary'>负责人</label>
                <span className='system-xs-regular ml-1 text-text-tertiary'></span>
              </div>
              {/* 负责人下拉多选 */}
              <Select
                closeMenuOnSelect={false}
                components={{
                  ...animatedComponents,
                  MenuList
                }}
                value={ownerList.filter(option => owner.includes(option.value))}
                isMulti
                options={ownerList}
                placeholder="选择负责人"
                classNamePrefix="react-select"
                isLoading={isLoadingOwners}
                isSearchable={true}
                onInputChange={handleOwnerSearchInputChange}
                onChange={(selectedOptions: any) => {
                  const values = selectedOptions ? selectedOptions.map((option: { value: string }) => option.value) : []
                  console.log('Selected values:', values)
                  setOwner(values)
                }}
                inputValue={ownerSearchInput}
                menuPlacement="auto"
                menuPortalTarget={typeof document !== 'undefined' ? document.body : null}
                styles={{
                  menuPortal: (base) => ({ ...base, zIndex: 9999 }),
                  menu: (base) => ({ ...base, overflowY: 'auto' }),
                  control: (baseStyles, state) => ({
                    ...baseStyles,
                    border: "none",
                    borderRadius: '8px',
                    backgroundColor: '#eff1f5',
                    borderColor: state.isFocused ? 'rgb(59 130 246 / 1)' : 'rgb(229 231 235 / 1)', // Tailwind's blue-500 and gray-200
                    boxShadow: state.isFocused ? '0 0 0 1px rgb(59 130 246 / 1)' : baseStyles.boxShadow,
                    '&:hover': {
                      borderColor: state.isFocused ? 'rgb(59 130 246 / 1)' : 'rgb(209 213 219 / 1)', // Tailwind's gray-300
                    },
                  }),
                  placeholder: (baseStyles) => ({
                    ...baseStyles,
                    fontSize: '13px',
                    color: 'rgb(156 163 175 / 1)', // Tailwind's gray-400, similar to typical input placeholders
                  }),
                }}
                // 优化大数据量下的性能设置
                maxMenuHeight={300}
                filterOption={(option, inputValue) => {
                  // 自定义过滤逻辑，避免在大数据量下频繁重渲染
                  return option.label.toLowerCase().includes(inputValue.toLowerCase())
                }}
              />
            </div>
            <div>
              <div className='mb-1 flex h-6 items-center'>
                <label className='system-sm-semibold text-text-secondary'>{t('app.newApp.captionDescription')}</label>
                <span className='system-xs-regular ml-1 text-text-tertiary'>({t('app.newApp.optional')})</span>
              </div>
              <Textarea
                className='resize-none'
                placeholder={t('app.newApp.appDescriptionPlaceholder') || ''}
                value={description}
                onChange={e => setDescription(e.target.value)}
              />
            </div>
          </div>
          {/* {isAppsFull && <AppsFull className='mt-4' loc='app-create' />} */}
          <div className='flex items-center justify-between pb-10 pt-5'>
            <div className='system-xs-regular flex cursor-pointer items-center gap-1 text-text-tertiary' onClick={onCreateFromTemplate}>
              <span>{t('app.newApp.noIdeaTip')}</span>
              <div className='p-[1px]'>
                <RiArrowRightLine className='h-3.5 w-3.5' />
              </div>
            </div>
            <div className='flex gap-2'>
              <Button onClick={onClose}>{t('app.newApp.Cancel')}</Button>
              <Button disabled={/*isAppsFull || */!name} className='gap-1' variant="primary" onClick={handleCreateApp}>
                <span>{t('app.newApp.Create')}</span>
                <div className='flex gap-0.5'>
                  <RiCommandLine size={14} className='system-kbd rounded-sm bg-components-kbd-bg-white p-0.5' />
                  <RiCornerDownLeftLine size={14} className='system-kbd rounded-sm bg-components-kbd-bg-white p-0.5' />
                </div>
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div className='relative flex h-full flex-1 shrink justify-start overflow-hidden'>
        <div className='absolute left-0 right-0 top-0 h-6 border-b border-b-divider-subtle 2xl:h-[139px]'></div>
        <div className='max-w-[760px] border-x border-x-divider-subtle'>
          <div className='h-6 2xl:h-[139px]' />
          <AppPreview mode={appMode} />
          <div className='absolute left-0 right-0 border-b border-b-divider-subtle'></div>
          <div className='flex h-[448px] w-[664px] items-center justify-center' style={{ background: 'repeating-linear-gradient(135deg, transparent, transparent 2px, rgba(16,24,40,0.04) 4px,transparent 3px, transparent 6px)' }}>
            <AppScreenShot show={appMode === 'agent-chat'} mode={appMode} />
            <AppScreenShot show={appMode === 'workflow'} mode={appMode} />
          </div>
          <div className='absolute left-0 right-0 border-b border-b-divider-subtle'></div>
        </div>
      </div>
    </div>
  </>
}
type CreateAppDialogProps = CreateAppProps & {
  show: boolean
}
const CreateAppModal = ({ show, onClose, onSuccess, onCreateFromTemplate }: CreateAppDialogProps) => {
  return (
    <FullScreenModal
      overflowVisible
      closable
      open={show}
      onClose={onClose}
    >
      <CreateApp onClose={onClose} onSuccess={onSuccess} onCreateFromTemplate={onCreateFromTemplate} />
    </FullScreenModal>
  )
}

export default CreateAppModal

type AppTypeCardProps = {
  icon: React.JSX.Element
  title: string
  description: string
  active: boolean
  onClick: () => void
}
function AppTypeCard({ icon, title, description, active, onClick }: AppTypeCardProps) {
  return <div
    className={
      cn(`relative box-content h-[84px] w-[191px] cursor-pointer rounded-xl
      border-[0.5px] border-components-option-card-option-border
      bg-components-panel-on-panel-item-bg p-3 shadow-xs hover:shadow-md`, active
        ? 'shadow-md outline outline-[1.5px] outline-components-option-card-option-selected-border'
        : '')
    }
    onClick={onClick}
  >
    {icon}
    <div className='system-sm-semibold mb-0.5 mt-2 text-text-secondary'>{title}</div>
    <div className='system-xs-regular text-text-tertiary'>{description}</div>
  </div>
}

function AppPreview({ mode }: { mode: AppMode }) {
  const { t } = useTranslation()
  const modeToPreviewInfoMap = {
    'agent-chat': {
      title: t('app.types.agent'),
      description: t('app.newApp.agentUserDescription'),
      link: 'https://docs.dify.ai/en/guides/application-orchestrate/agent',
    },
    'workflow': {
      title: t('app.types.workflow'),
      description: t('app.newApp.workflowUserDescription'),
      link: 'https://docs.dify.ai/en/guides/workflow/README',
    }
  }
  const previewInfo = modeToPreviewInfoMap[mode]
  return <div className='px-8 py-4'>
    <h4 className='system-sm-semibold-uppercase text-text-secondary'>{previewInfo.title}</h4>
    <div className='system-xs-regular mt-1 min-h-8 max-w-96 text-text-tertiary'>
      <span>{previewInfo.description}</span>
      {previewInfo.link && <Link target='_blank' href={previewInfo.link} className='ml-1 text-text-accent'>{t('app.newApp.learnMore')}</Link>}
    </div>
  </div>
}

function AppScreenShot({ mode, show }: { mode: AppMode; show: boolean }) {
  const { theme } = useTheme()
  const modeToImageMap = {
    'agent-chat': 'Agent',
    'workflow': 'Workflow',
  }
  return <picture>
    <source media="(resolution: 1x)" srcSet={`${WEB_PREFIX}/screenshots/${theme}/${modeToImageMap[mode]}.png`} />
    <source media="(resolution: 2x)" srcSet={`${WEB_PREFIX}/screenshots/${theme}/${modeToImageMap[mode]}@2x.png`} />
    <source media="(resolution: 3x)" srcSet={`${WEB_PREFIX}/screenshots/${theme}/${modeToImageMap[mode]}@3x.png`} />
    <Image className={show ? '' : 'hidden'}
      src={`${WEB_PREFIX}/screenshots/${theme}/${modeToImageMap[mode]}.png`}
      alt='App Screen Shot'
      width={664} height={448} />
  </picture>
}
