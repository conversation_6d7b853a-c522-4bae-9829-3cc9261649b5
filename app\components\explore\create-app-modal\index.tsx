'use client'
import React, { useCallback, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { RiCloseLine, RiCommandLine, RiCornerDownLeftLine } from '@remixicon/react'
import { useDebounceFn, useKeyPress } from 'ahooks'
import AppIconPicker from '../../base/app-icon-picker'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import Input from '@/app/components/base/input'
import Textarea from '@/app/components/base/textarea'
import Switch from '@/app/components/base/switch'
import Toast from '@/app/components/base/toast'
import AppIcon from '@/app/components/base/app-icon'
import { useProviderContext } from '@/context/provider-context'
import AppsFull from '@/app/components/billing/apps-full-in-dialog'
import type { AppIconType } from '@/types/app'
import { noop } from 'lodash-es'
import Select from 'react-select';
import makeAnimated from 'react-select/animated';
import { FixedSizeList as List } from 'react-window';
import { get } from '@/service/base'

// 创建动画组件
const animatedComponents = makeAnimated();

// 创建虚拟列表菜单组件
const MenuList = (props: any) => {
  // 安全检查，确保有子元素
  if (!props.children || !Array.isArray(props.children) || props.children.length === 0) {
    return <div>无数据</div>;
  }

  // 计算列表高度，但最大不超过 maxHeight
  const height = Math.min(35 * props.children.length, props.maxHeight);

  return (
    <List
      width="100%"
      height={height}
      itemCount={props.children.length}
      itemSize={35}
      initialScrollOffset={0}
    >
      {({ index, style }) => (
        <div style={style}>
          {props.children[index]}
        </div>
      )}
    </List>
  );
};

export type CreateAppModalProps = {
  show: boolean
  isEditModal?: boolean
  appName: string
  appDescription: string
  appIconType: AppIconType | null
  appIcon: string
  appIconBackground?: string | null
  appIconUrl?: string | null
  appMode?: string
  appUseIconAsAnswerIcon?: boolean
  appManagerList?: string[] // 添加负责人列表属性
  onConfirm: (info: {
    name: string
    icon_type: AppIconType
    icon: string
    icon_background?: string
    description: string
    use_icon_as_answer_icon?: boolean
    manager_list?: string[] // 添加负责人列表参数
  }) => Promise<void>
  confirmDisabled?: boolean
  onHide: () => void
}

const CreateAppModal = ({
  show = false,
  isEditModal = false,
  appIconType,
  appIcon: _appIcon,
  appIconBackground,
  appIconUrl,
  appName,
  appDescription,
  appMode,
  appUseIconAsAnswerIcon,
  appManagerList = [], // 默认为空数组
  onConfirm,
  confirmDisabled,
  onHide,
}: CreateAppModalProps) => {
  const { t } = useTranslation()

  const [name, setName] = React.useState(appName)
  const [appIcon, setAppIcon] = useState(
    () => appIconType === 'image'
      ? { type: 'image' as const, fileId: _appIcon, url: appIconUrl }
      : { type: 'emoji' as const, icon: _appIcon, background: appIconBackground },
  )
  const [showAppIconPicker, setShowAppIconPicker] = useState(false)
  const [description, setDescription] = useState(appDescription || '')
  const [useIconAsAnswerIcon, setUseIconAsAnswerIcon] = useState(appUseIconAsAnswerIcon || false)
  const [owner, setOwner] = useState<string[]>(appManagerList || []) // 负责人状态
  const [ownerList, setOwnerList] = useState<Array<{ value: string; label: string }>>([]) // 负责人选项列表
  const [isLoadingOwners, setIsLoadingOwners] = useState(false) // 加载状态
  const [ownerSearchInput, setOwnerSearchInput] = useState('') // 搜索关键词

  const { plan, enableBilling } = useProviderContext()
  const isAppsFull = (enableBilling && plan.usage.buildApps >= plan.total.buildApps)

  // 从外部API获取负责人列表
  useEffect(() => {
    const fetchOwners = async () => {
      try {
        setIsLoadingOwners(true)
        // 获取负责人数据
        const data = await get<string[]>('/account/list')
        // 确保数据是数组且不为空
        if (data && Array.isArray(data)) {
          setOwnerList(data.map((item: string) => ({
            value: item,
            label: item
          })))
        } else {
          setOwnerList([])
          console.error('获取到的负责人数据格式不正确:', data)
        }
      } catch (error) {
        console.error('Failed to fetch owners:', error)
        setOwnerList([])
      } finally {
        setIsLoadingOwners(false)
      }
    }

    // 添加防抖延迟，避免频繁请求
    const timer = setTimeout(fetchOwners, 300)
    return () => clearTimeout(timer)
  }, [ownerSearchInput]) // 添加搜索关键词作为依赖项

  // 处理搜索输入变化
  const handleOwnerSearchInputChange = (inputValue: string) => {
    setOwnerSearchInput(inputValue)
  }

  const submit = useCallback(() => {
    if (!name.trim()) {
      Toast.notify({ type: 'error', message: t('explore.appCustomize.nameRequired') })
      return
    }
    onConfirm({
      name,
      icon_type: appIcon.type,
      icon: appIcon.type === 'emoji' ? appIcon.icon : appIcon.fileId,
      icon_background: appIcon.type === 'emoji' ? appIcon.background! : undefined,
      description,
      use_icon_as_answer_icon: useIconAsAnswerIcon,
      manager_list: owner && owner.length > 0 ? owner : [], // 提交负责人列表
    })
    onHide()
  }, [name, appIcon, description, useIconAsAnswerIcon, onConfirm, onHide, t, owner])

  const { run: handleSubmit } = useDebounceFn(submit, { wait: 300 })

  useKeyPress(['meta.enter', 'ctrl.enter'], () => {
    if (show && !(!isEditModal && isAppsFull) && name.trim())
      handleSubmit()
  })

  useKeyPress('esc', () => {
    if (show)
      onHide()
  })

  return (
    <>
      <Modal
        isShow={show}
        onClose={noop}
        className='relative !max-w-[480px] px-8'
      >
        <div className='absolute right-4 top-4 cursor-pointer p-2' onClick={onHide}>
          <RiCloseLine className='h-4 w-4 text-text-tertiary' />
        </div>
        {isEditModal && (
          <div className='mb-9 text-xl font-semibold leading-[30px] text-text-primary'>{t('app.editAppTitle')}</div>
        )}
        {!isEditModal && (
          <div className='mb-9 text-xl font-semibold leading-[30px] text-text-primary'>{t('explore.appCustomize.title', { name: appName })}</div>
        )}
        <div className='mb-9'>
          {/* icon & name */}
          <div className='pt-2'>
            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>{t('app.newApp.captionName')}</div>
            <div className='flex items-center justify-between space-x-2'>
              <AppIcon
                size='large'
                onClick={() => { setShowAppIconPicker(true) }}
                className='cursor-pointer'
                iconType={appIcon.type}
                icon={appIcon.type === 'image' ? appIcon.fileId : appIcon.icon}
                background={appIcon.type === 'image' ? undefined : appIcon.background}
                imageUrl={appIcon.type === 'image' ? appIcon.url : undefined}
              />
              <Input
                value={name}
                onChange={e => setName(e.target.value)}
                placeholder={t('app.newApp.appNamePlaceholder') || ''}
                className='h-10 grow'
              />
            </div>
          </div>
          {/* 负责人 */}
          <div className='pt-2'>
            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>负责人</div>
            <Select
              closeMenuOnSelect={false}
              components={{
                ...animatedComponents,
                MenuList
              }}
              value={ownerList.filter(option => owner.includes(option.value))}
              isMulti
              options={ownerList}
              placeholder="选择负责人"
              classNamePrefix="react-select"
              isLoading={isLoadingOwners}
              isSearchable={true}
              onInputChange={handleOwnerSearchInputChange}
              onChange={(selectedOptions: any) => {
                const values = selectedOptions ? selectedOptions.map((option: { value: string }) => option.value) : []
                console.log('Selected values:', values)
                setOwner(values)
              }}
              inputValue={ownerSearchInput}
              menuPlacement="auto"
              menuPortalTarget={typeof document !== 'undefined' ? document.body : null}
              styles={{
                menuPortal: (base) => ({ ...base, zIndex: 9999 }),
                menu: (base) => ({ ...base, overflowY: 'auto' }),
                control: (baseStyles, state) => ({
                  ...baseStyles,
                  border: "none",
                  borderRadius: '8px',
                  backgroundColor: '#eff1f5',
                  borderColor: state.isFocused ? 'rgb(59 130 246 / 1)' : 'rgb(229 231 235 / 1)',
                  boxShadow: state.isFocused ? '0 0 0 1px rgb(59 130 246 / 1)' : baseStyles.boxShadow,
                  '&:hover': {
                    borderColor: state.isFocused ? 'rgb(59 130 246 / 1)' : 'rgb(209 213 219 / 1)',
                  },
                }),
                placeholder: (baseStyles) => ({
                  ...baseStyles,
                  fontSize: '13px',
                  color: 'rgb(156 163 175 / 1)',
                }),
              }}
              maxMenuHeight={300}
              filterOption={(option, inputValue) => {
                return option.label.toLowerCase().includes(inputValue.toLowerCase())
              }}
            />
          </div>
          {/* description */}
          <div className='pt-2'>
            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>{t('app.newApp.captionDescription')}</div>
            <Textarea
              className='resize-none'
              placeholder={t('app.newApp.appDescriptionPlaceholder') || ''}
              value={description}
              onChange={e => setDescription(e.target.value)}
            />
          </div>
          {/* answer icon */}
          {isEditModal && (appMode === 'chat' || appMode === 'advanced-chat' || appMode === 'agent-chat') && (
            <div className='pt-2'>
              <div className='flex items-center justify-between'>
                <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>{t('app.answerIcon.title')}</div>
                <Switch
                  defaultValue={useIconAsAnswerIcon}
                  onChange={v => setUseIconAsAnswerIcon(v)}
                />
              </div>
              <p className='body-xs-regular text-text-tertiary'>{t('app.answerIcon.descriptionInExplore')}</p>
            </div>
          )}
          {!isEditModal && isAppsFull && <AppsFull className='mt-4' loc='app-explore-create' />}
        </div>
        <div className='flex flex-row-reverse'>
          <Button
            disabled={(!isEditModal && isAppsFull) || !name.trim() || confirmDisabled}
            className='ml-2 w-24 gap-1'
            variant='primary'
            onClick={handleSubmit}
          >
            <span>{!isEditModal ? t('common.operation.create') : t('common.operation.save')}</span>
            <div className='flex gap-0.5'>
              <RiCommandLine size={14} className='system-kbd rounded-sm bg-components-kbd-bg-white p-0.5' />
              <RiCornerDownLeftLine size={14} className='system-kbd rounded-sm bg-components-kbd-bg-white p-0.5' />
            </div>
          </Button>
          <Button className='w-24' onClick={onHide}>{t('common.operation.cancel')}</Button>
        </div>
      </Modal>
      {showAppIconPicker && <AppIconPicker
        onSelect={(payload) => {
          setAppIcon(payload)
          setShowAppIconPicker(false)
        }}
        onClose={() => {
          setAppIcon(appIconType === 'image'
            ? { type: 'image' as const, url: appIconUrl, fileId: _appIcon }
            : { type: 'emoji' as const, icon: _appIcon, background: appIconBackground })
          setShowAppIconPicker(false)
        }}
      />}
    </>
  )
}

export default CreateAppModal
