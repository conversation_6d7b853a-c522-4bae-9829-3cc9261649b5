import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

const NECESSARY_DOMAIN = '*.sentry.io http://localhost:* http://127.0.0.1:* https://analytics.google.com googletagmanager.com *.googletagmanager.com https://www.google-analytics.com https://api.github.com'

// 默认的系统特性响应，用于自动跳过认证
const DEFAULT_SYSTEM_FEATURES_RESPONSE = {
  sso_enforced_for_signin: false,
  sso_enforced_for_signin_protocol: '',
  sso_enforced_for_web: false,
  sso_enforced_for_web_protocol: '',
  enable_web_sso_switch_component: false,
  enable_marketplace: true,
  enable_email_code_login: true,
  enable_email_password_login: true,
  enable_social_oauth_login: false,
  is_allow_create_workspace: true,
  is_allow_register: true,
  is_email_setup: true,
  license: {
    status: 'ACTIVE',
    expired_at: '',
  }
}

const wrapResponseWithXFrameOptions = (response: NextResponse, pathname: string) => {
  // 允许作为iframe嵌入，设置为ALLOWALL允许任何站点嵌入
  response.headers.delete('X-Frame-Options') // 移除此头部以允许任何站点嵌入
  
  // 添加允许跨域的头部
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  return response
}

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl
  
  // 检测到workcode参数，且路径为signin或根路径时，直接重定向到apps页面
  const workcode = searchParams.get('workcode')
  if (workcode && (pathname === '/signin' || pathname === '/' || pathname.includes('_rsc'))) {
    const url = request.nextUrl.clone()
    url.pathname = '/apps'
    // 保留workcode参数
    url.searchParams.set('workcode', workcode)
    return NextResponse.redirect(url)
  }

  // 对 system-features API请求进行特殊处理，返回默认配置
  if (workcode && pathname.includes('/system-features')) {
    return NextResponse.json(DEFAULT_SYSTEM_FEATURES_RESPONSE)
  }
  
  const requestHeaders = new Headers(request.headers)
  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  })

  const isWhiteListEnabled = !!process.env.NEXT_PUBLIC_CSP_WHITELIST && process.env.NODE_ENV === 'production'
  if (!isWhiteListEnabled)
    return wrapResponseWithXFrameOptions(response, pathname)

  const whiteList = `${process.env.NEXT_PUBLIC_CSP_WHITELIST} ${NECESSARY_DOMAIN}`
  const nonce = Buffer.from(crypto.randomUUID()).toString('base64')
  const csp = `'nonce-${nonce}'`

  const scheme_source = 'data: mediastream: blob: filesystem:'

  const cspHeader = `
    default-src 'self' ${scheme_source} ${csp} ${whiteList};
    connect-src 'self' ${scheme_source} ${csp} ${whiteList} *;
    script-src 'self' ${scheme_source} ${csp} ${whiteList};
    style-src 'self' 'unsafe-inline' ${scheme_source} ${whiteList};
    worker-src 'self' ${scheme_source} ${csp} ${whiteList};
    media-src 'self' ${scheme_source} ${csp} ${whiteList};
    img-src * data:;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors *;
    upgrade-insecure-requests;
`
  // Replace newline characters and spaces
  const contentSecurityPolicyHeaderValue = cspHeader
    .replace(/\s{2,}/g, ' ')
    .trim()

  requestHeaders.set('x-nonce', nonce)

  requestHeaders.set(
    'Content-Security-Policy',
    contentSecurityPolicyHeaderValue,
  )

  response.headers.set(
    'Content-Security-Policy',
    contentSecurityPolicyHeaderValue,
  )

  return wrapResponseWithXFrameOptions(response, pathname)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    {
      // source: '/((?!api|_next/static|_next/image|favicon.ico).*)',
      source: '/((?!_next/static|_next/image|favicon.ico).*)',
      // source: '/(.*)',
      // missing: [
      //   { type: 'header', key: 'next-router-prefetch' },
      //   { type: 'header', key: 'purpose', value: 'prefetch' },
      // ],
    },
  ],
}
